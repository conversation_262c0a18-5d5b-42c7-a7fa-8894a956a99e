<!-- <p>dynamicform works!</p> -->
<form [formGroup]="form" (ngSubmit)="onSubmit()">
    <ng-container [ngSwitch]="topic">
        <ng-container *ngSwitchCase="'waste'">
            <div class="row">
                <div class="col-md-6 mb-3" *ngFor="let control of jsonForm">
                    <div *ngIf="control.question_type === 'toggle' || control.question_type === 'radio' ">
                        <!-- <label [for]="control.id" >{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></label> -->
                        <span class="d-flex justify-content-between">
                            <label [for]="control.id" >{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></label>
                            <mat-icon placement="left" ngbTooltip="{{control.hint}}" *ngIf="control.hint" style="opacity: 0.5;">info</mat-icon>
                        </span>
                        <select [formControlName]="control.id" [id]="control.id" class="form-control py-2 border-7" (change)="optionsChange($event.target.value, control)">
                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                            <!-- <option *ngFor="let option of control.options" [value]="option | json">{{option.name}}</option> -->
                            <option *ngFor="let option of getContent(control)?.options" [value]="option.options_id">{{option.name}}</option>
                        </select>
                        <!-- <div *ngIf="control.is_required"> -->
                            <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                                {{'kSelectOne' | translate}}
                            </div>
                        <!-- </div> -->
                    </div>
                    <div *ngIf="control.question_type === 'numeric'">
                        <label [for]="control.id" class="form-label fs-6 fw-semibold">{{getContent(control)?.content}}</label>
                        <input type="number" [appPreventMinues]="true" [formControlName]="control.id" class="form-control py-2 border-7" [id]="control.id" placeholder="Enter your valid answer here">
                        <div *ngIf="submitted && f[control.id]?.errors?.required" class="mt-2 invalid-feedback">
                            {{'kKindlyAnswerAboveQuestion' | translate}}
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
        <ng-container *ngSwitchCase="'employee'">
            <div class="row">
                <div class="col-md-6 mb-3" *ngFor="let control of jsonForm">
                    <div *ngIf="control.question_type === 'toggle' || control.question_type === 'radio' ">
                        <label [for]="control.id" >{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></label>
                        <select [formControlName]="control.id" [id]="control.id" class="form-control py-2 border-7" (change)="optionsChange($event.target.value, control)">
                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                            <!-- <option *ngFor="let option of control.options" [value]="option | json">{{option.name}}</option> -->
                            <option *ngFor="let option of getContent(control)?.options" [value]="option.options_id">{{option.name}}</option>
                        </select>
                        <!-- <div *ngIf="control.is_required"> -->
                            <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                                {{'kSelectOne' | translate}}
                            </div>
                        <!-- </div> -->
                    </div>
                    <div *ngIf="control.question_type === 'numeric'">
                        <label [for]="control.id" class="form-label fs-6 fw-semibold d-flex justify-content-between"><span class="d-inline-block">{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></span>
                            <span>
                                <mat-icon placement="bottom" [ngbTooltip]="getContent(control)?.hint" *ngIf="getContent(control)?.hint !==''" style="opacity: 0.5;">info</mat-icon>
                            </span>
                        </label>
                        <input type="number" [appPreventMinues]="true" [formControlName]="control.id" class="form-control py-2 border-7" [id]="control.id" (change)="emitChange.emit()" placeholder="Enter your valid answer here">
                        <div *ngIf="submitted && f[control.id]?.errors?.required" class="mt-2 invalid-feedback">
                            {{'kKindlyAnswerAboveQuestion' | translate}}
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
        <ng-container *ngSwitchCase="'refrigerant'">
            <div class="row">
                <div class="col-md-6 mb-3" *ngFor="let control of jsonForm">
                    <div *ngIf="control.question_type === 'toggle' || control.question_type === 'radio' ">
                        <span class="d-flex justify-content-between">
                            <label [for]="control.id" >{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></label>
                            <mat-icon placement="bottom" ngbTooltip="{{control.hint}}" *ngIf="control.hint" style="opacity: 0.5;">info</mat-icon>
                        </span>
                        <select [formControlName]="control.id" [id]="control.id" class="form-control py-2 border-7" (change)="optionsChange($event.target.value, control)">
                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                            <!-- <option *ngFor="let option of control.options" [value]="option | json">{{option.name}}</option> -->
                            <option *ngFor="let option of getContent(control)?.options" [value]="option.options_id">{{option.name}}</option>
                        </select>
                        <!-- <div *ngIf="control.is_required"> -->
                            <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                                {{'kSelectOne' | translate}}
                            </div>
                        <!-- </div> -->
                    </div>
                    <div *ngIf="control.question_type === 'numeric'">
                        <label [for]="control.id" class="form-label fs-6 fw-semibold">{{getContent(control)?.content}}</label>
                        <input type="number" [appPreventMinues]="true" [formControlName]="control.id" class="form-control py-2 border-7" [id]="control.id" placeholder="Enter your valid answer here">
                        <div *ngIf="submitted && f[control.id]?.errors?.required" class="mt-2 invalid-feedback">
                            {{'kKindlyAnswerAboveQuestion' | translate}}
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
        <ng-container *ngSwitchCase="'energy'">
            <div class="w-100 mb-3">
                <table class="w-100">
                    <thead>
                        <tr style="height: 40px;">
                            <th class="w-40 text-center headercolor fw-light" style="border-top-left-radius: 20px;">
                                <p class="m-0" [ngClass]="{'py-4': !this.service.showCalculation}">{{'kQuestions' | translate}}</p>
                            </th>
                            <th class="w-20 text-center headercolor border-0" [ngClass]="{'border-right': !this.service.showCalculation}" >
                                <p class="m-0" [ngClass]="{'py-4': !this.service.showCalculation}">{{'kValue' | translate}}</p>
                            </th>
                            <!-- <th class="text-center headercolor" *ngIf="this.service.showCalculation">
                                <p class="m-0" style="padding-block: 0.8rem">tCO2e Use of sold products - location-based</p>
                            </th>
                            <th class="text-center headercolor" style="padding-right: 1rem;border-top-right-radius: 20px;" *ngIf="this.service.showCalculation">
                                <p class="m-0" style="padding-block: 0.8rem">tCO2e Use of sold products market-based</p>
                            </th> -->
                        </tr>
                    </thead>
                    <tbody>
                        <ng-container *ngIf="jsonForm?.length">
                            <tr *ngFor="let control of jsonForm;let i = index;">
                                <ng-container
                                    *ngIf="control.question_type === 'toggle' || control.question_type === 'radio' ">
                                    <td class="w-40">
                                        <label [for]="control.id">{{getContent(control)?.content}}<span *ngIf="control.is_required" class="text-danger ml-1">*</span></label>
                                    </td>
                                    <td class="w-20" [ngClass]="{'pr-3': !this.service.showCalculation}" *ngIf="!control.isCalExclude"> 
                                        <select [formControlName]="control.id" style="width:90%;padding-inline:1px;" [id]="control.id"
                                            class="form-control py-2 border-7"
                                            (change)="optionsChange($event.target.value, control)">
                                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                                            <option *ngFor="let option of getContent(control)?.options"
                                                [value]="option.options_id">{{option.name}}</option>
                                        </select>
                                        <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                                            {{'kSelectOne' | translate}}
                                        </div>
                                    </td>
                                    <!-- for multi select -->
                                    <td class="w-20" [ngClass]="{'pr-3': !this.service.showCalculation}" *ngIf="control.isCalExclude"> 
                                        <ngx-select-dropdown
                                            (change)="optionsChange($event, control)"
                                            [multiple]="true"
                                            [formControlName]="control.id" 
                                            [config]="config"
                                            [options]="getContent(control)?.options">
                                        </ngx-select-dropdown>
                                        <!-- <select [formControlName]="control.id" style="width:90%;padding-inline:1px;" [id]="control.id"
                                            class="form-control py-2 border-7"
                                            (change)="optionsChange($event.target.value, control)">
                                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                                            <option *ngFor="let option of getContent(control)?.options"
                                                [value]="option.options_id">{{option.name}}</option>
                                        </select> -->
                                        <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                                            {{'kSelectOne' | translate}}
                                        </div>
                                    </td>
                                    <!-- <td class="text-center" *ngIf="this.service.showCalculation"> -- </td>
                                    <td class="text-center" *ngIf="this.service.showCalculation"> --</td> -->
                                </ng-container>
                                <ng-container *ngIf="control.question_type === 'numeric'">
                                    <td class="w-40">
                                        <label [for]="control.id" class="form-label fs-6 fw-semibold">{{getContent(control)?.content}}</label>
                                    </td>
                                    <td class="w-20" [ngClass]="{'pr-3': !this.service.showCalculation}">
                                        <div class="d-flex">
                                            <input type="number" [appPreventMinues]="true" style="width:80%;" min="0" [formControlName]="control.id"
                                                class="form-control py-2 border-7" [id]="control.id"
                                                (change)="emissionValueCalc($event.target.value, control)"
                                                placeholder="Enter your valid answer here">
                                            <div style="width: 20%;" *ngIf="control?.unit"
                                                class="d-flex justify-content-center align-items-center"><span>{{control?.unit}}</span>
                                            </div>
                                            <div style="width: 20%;" *ngIf="control?.unit === null && control.emission_type !== null"
                                                class="d-flex justify-content-center align-items-center ml-2">
                                                <select [(ngModel)]="unitForEnergy" style="padding-inline:1px;" [ngModelOptions]="{standalone: true}"
                                                    class="form-control py-2 border-7" (change)="onUnitChange(i, control)">
                                                    <option value="kWh" selected>kWh</option>
                                                    <option value="m3">m<sup>3</sup></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div *ngIf="submitted && f[control.id]?.errors?.required" class="mt-2 invalid-feedback">
                                            {{'kKindlyAnswerAboveQuestion' | translate}}
                                        </div>
                                    </td>
                                    <!-- <td style="padding-right: 1rem;" *ngIf="this.service.showCalculation">
                                        <div class="d-flex justify-content-center align-items-center"*ngIf="control.tCO2eLB">
                                            <p>{{control.tCO2eLB || '-'}}</p>
                                        </div>
                                    </td>
                                    <td style="padding-right: 1rem;" *ngIf="this.service.showCalculation">
                                        <div class="d-flex justify-content-center align-items-center" style="width:50%;" *ngIf="control.tCO2eMB">
                                            <p>{{control.tCO2eMB || '-'}}</p>
                                        </div>
                                    </td> -->
                                </ng-container>
                            </tr>
                        </ng-container>
                        <tr *ngIf="!jsonForm?.length">
                            <td>
                                <p>{{'kNoQuestionsFound' | translate}}</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- <div class="row">
                <div class="col-md-6"></div>
                <div class="col-md-6 d-flex">
                    <div style="width:50%;">tCO2e Use of sold products - location-based</div>
                    <div style="width:50%;">tCO2e Use of sold products market-based</div>
                </div>
            </div>
            <div class="row" *ngFor="let control of jsonForm;let i = index;">
                <div class="col-md-6 mb-3">
                    <div *ngIf="control.question_type === 'toggle' || control.question_type === 'radio' ">
                        <label [for]="control.id" >{{getContent(control)?.content}}</label>
                        <select [formControlName]="control.id" style="width:50%;" [id]="control.id" class="form-control py-2 border-7" (change)="optionsChange($event.target.value, control)">
                            <option value="" disabled>{{'kChooseAnyOne' | translate}}</option>
                            <option *ngFor="let option of getContent(control)?.options" [value]="option.options_id">{{option.name}}</option>
                        </select>
                        <div *ngIf="submitted && f[control.id]?.errors" class="mt-2 invalid-feedback">
                            {{'kSelectOne' | translate}}
                        </div>
                    </div>
                    <div *ngIf="control.question_type === 'numeric'">
                        <label [for]="control.id" class="form-label fs-6 fw-semibold">{{getContent(control)?.content}}</label>
                        <div class="d-flex">
                            <input type="number" [appPreventMinues]="true" style="width:50%;" min="0" [formControlName]="control.id" class="form-control py-2 border-7" [id]="control.id" (change)="emissionValueCalc($event.target.value, control)" placeholder="Enter your valid answer here">
                            <div style="width: 10%;" *ngIf="control?.unit" class="d-flex justify-content-center align-items-center"><span>{{control?.unit}}</span></div>
                            <div style="width: 16%;" *ngIf="control?.unit == null && control.emission_type != null" class="d-flex justify-content-center align-items-center ml-2">
                                <select [(ngModel)]="unitForEnergy" [ngModelOptions]="{standalone: true}" class="form-control py-2 border-7" (change)="onUnitChange(i, control)">
                                    <option value="kWh" selected>kWh</option>
                                    <option value="m3">m<sup>3</sup></option>
                                </select>
                            </div>
                        </div>
                        <div *ngIf="submitted && f[control.id]?.errors?.required" class="mt-2 invalid-feedback">
                            {{'kKindlyAnswerAboveQuestion' | translate}}
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-5">
                    <div class="d-flex h-100">
                        <div class="d-flex justify-content-center align-items-center" style="width:50%;" *ngIf="control.tCO2eLB"><p>{{control.tCO2eLB || '-'}}</p></div>
                        <div class="d-flex justify-content-center align-items-center" style="width:50%;" *ngIf="control.tCO2eMB"><p>{{control.tCO2eMB || '-'}}</p></div>
                    </div>
                </div>
            </div> -->
        </ng-container>
    </ng-container>

</form>
<ng-template #errorPopUP>
  <div class="modal-header">
    <div class="row ">
      <div class="col-12">
        <h2 class="my-2 text-danger">{{'kError' | translate}}</h2>
      </div>
    </div>
    <button mat-flat-button type="button" (click)="close()"><mat-icon>close</mat-icon></button>
  </div>
  <div class="modal-body" style="height: 85px;overflow: auto;">
    <div class="row">
      <div class="col-12">
        <h3 class="px-2">{{'kNotgreater' | translate}}</h3>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button mat-flat-button type="button" class="mr-3" (click)="close()">{{'kClose' | translate}}</button>
  </div>
</ng-template>

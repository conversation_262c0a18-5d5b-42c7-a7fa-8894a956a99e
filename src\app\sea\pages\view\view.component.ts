import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  OnDestroy,
  TemplateRef,
} from "@angular/core";
import { Observable, Subscription, of } from "rxjs";
import { AuthService } from "src/app/shared/services/auth.service";
import { MatDialog } from "@angular/material/dialog";
import {
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
} from "@angular/forms";
import { SeasiteService } from "../../services/seasite.service";
import * as _ from "lodash";
import { NgxSpinnerService } from "ngx-spinner";
import { PptgenerateComponent } from "src/app/shared/pptgenerate/pptgenerate.component";
import { ActivatedRoute, Router } from "@angular/router";
import { LanguageTranslateService } from "src/app/carbon/services/language-translate.service";
import { TranslateService } from "@ngx-translate/core";
import { BestPracticeLibraryComponent } from "src/app/shared/components/best-practice-library/best-practice-library.component";
import { DisableGlobalLoaderService } from "src/app/shared/services/disable-global-loader.service";
import { SeaServiceComponent } from "src/app/shared/components/sea-service/sea-service.component";
import { ToastrService } from "ngx-toastr";
import { SiteManagementService } from "src/app/shared/services/site-management.service";
import { SiteService } from "src/app/shared/services/site.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ExportToCsv } from "export-to-csv";
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { CountryService } from "src/app/carbon/services/country.service";
import { ServicesService } from "src/app/shared/services/services.service";
import { SeaLiteQuestionsComponent } from "../sea-lite-questions/sea-lite-questions.component";

@Component({
  selector: "app-view",
  templateUrl: "./view.component.html",
  styleUrls: ["./view.component.scss"],
})
export class ViewComponent implements OnInit {
  @ViewChild(PptgenerateComponent) PptFileDownload: PptgenerateComponent;
  @ViewChild("staticCsvPop") staticCsvPop: TemplateRef<any>;
  @ViewChild("panel") panel;
  date = new Date();
  hideFilter = false;
  task: any = {
    name: "kAll",
    completed: false,
    subtasks: [
      { name: "kCradleToGate", completed: true },
      { name: "kScope1And2", completed: true },
      { name: "kToGrave", completed: true },
    ],
  };
  approvedPop = false;
  allComplete = true;
  popLoader = false;
  popDisabled = true;
  type: any;
  showError = false;
  services: any;
  emissionFactList: any[];
  csvPdfOption: any;
  roleName: any;
  approvedStatus: any;
  approoverName: any;
  siteDetails: any;
  tableViewData: any = [];
  filterResul = false;
  notificationData: { id: any; title: any; body: any };
  emptyArray = [];
  finalFilter: any = [];
  downloadFile: any;
  minimize = false;
  profileLang: any;
  fildFoodSubService: boolean;
  siteManagementDetails: any;
  siteManagement: any;
  firstTotalInputValue: number;
  secondTotalInputValue: number;
  siteAddress: any;
  SiteManagementData: any;
  consumerNumber: any;
  siteStatus: boolean = false;
  bestPracticesForQuestions: any = [];
  progressWidth2: number;
  siteCategoryId: any;
  dialogRef: any;
  isShowPopup: boolean = true;
  @Input()
  set views(view: any) {
    this.type = view;
  }
  get views(): any {
    return this.type;
  }
  questionResult: any[];
  isShow = false;
  @Output() goGeneral: EventEmitter<any> = new EventEmitter();
  generalInfo: any;
  groupName: any = [];
  subscription: Subscription = new Subscription();
  answerResult: any = [];
  activeQuesLists = {};
  kContactYorAdmin: any;
  EmailAddr = "";
  userDetails: any;
  siteId: any;
  userServices: any = [];
  SiteFilterData: any = [];
  servicesDatas: any = [];
  servicesData: any = [];
  serviceForm: UntypedFormGroup;
  servicesFormArray: UntypedFormArray;
  percentageTotalScore: any;
  percentageBenchmarkTotal: any;
  maxScore: any;
  areaUnit = ["m2", "sqft", "acres", "Hectares"];
  unit: any;
  panelOpenState = false;
  tooltipText: string = "";
  isRotated: boolean = false;
  showGSA: any;
  filter: any = [];
  filterDat = [
    { id: 1, name: "Global", kkeys: "kGlobal" },
    { id: 2, name: "segment", kkeys: "kSegment" },
    { id: 3, name: "ila", kkeys: "kGsa" },
    { id: 4, name: "country", kkeys: "kMyCountry" },
    { id: 5, name: "countrySegment", kkeys: "kMyCountryMySegment" },
  ];
  defaultFilter = 1;
  parentTopic: any = [];
  displayedRows$: Observable<any>;
  bestLibraryData: any = [];
  filterdBest: any = [];
  bestPracticeData: any = [];
  cColor: any = 0;
  tableColora = {
    "Energy Management": "#fef4e8",
    "Waste Management": "#eaf8f2",
    "Water Management": "#eef8fc",
    "Sustainable Eating": "#e9f5ea",
    "General Initiatives": "#fdeae9",
  };
  badgesAllQuestion: any;
  performance: any;
  address: any;
  siteManagementValue: any;
  getKPIPerformanceTopic: any[] = [];
  hideStyle: boolean = false;
  dialogRefCSVOrPpt: any;
  reportOption = 0;
  options = {
    fieldSeparator: ",",
    quoteStrings: '"',
    decimalSeparator: ".",
    showLabels: true,
    useTextFile: false,
    useBom: true,
    useKeysAsHeaders: true,
    filename: "SEA KPI Performance Report",
  };
  email: any;
  isPerformanceClicked: boolean = false;
  city: any = '';
  country: any = '';
  reportOptionSelected: any;
  typeSelected: any;
  liteQnsAnsCnt: number = 0;
  totalLiteQnsCnt: number = 0;
  isLogicalQuestions: boolean[] = [];
  siteCountry: string = 'India';
  excelData: any[] = [];
  implemented: any[] = [];
  notImplemented: any[] = [];
  isSodexoLiveSite: boolean = false;
  allLiteQns: any[] = [];
  allQns: any[] = [];

  constructor(
    public oAuth: AuthService,
    public site: SeasiteService,
    private siteService: SiteService,
    private dialog: MatDialog,
    private spinner: NgxSpinnerService,
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private lts: LanguageTranslateService,
    private dgls: DisableGlobalLoaderService,
    private toaster: ToastrService,
    private SiteManagementService: SiteManagementService,
    private modalService: NgbModal,
    private countryService: CountryService,
    private service: ServicesService
  ) {
    translate.use("en");
    translate.setTranslation("en", this.lts.state);
    this.getSiteStatus();
  }
  ngOnInit(): void {
    this.getSiteDeatailsData();
    this.dgls.unload();
    this.showFilterData();
    // this.getSiteManagementDetails();
    // this.getSiteDetails();
    this.getProfileEmail();
    
    this.getPracticesQuestion();
    this.extractKPIData();
    this.service.progressWidth$.subscribe(progress => {
      if(progress){
        this.progressWidth2 = isNaN(progress.progressWidth2) ? 0 : progress.progressWidth2;
      }
    });

    this.receiveLiteDataFromHeader();
    this.siteCountry = this.site.managerDetails.address.country;
  }

  ngOnChanges(){
    this.site.currentReportData.subscribe(data => {
      this.reportOptionSelected = data.reportOption;
      this.typeSelected = data.type;

      if(this.reportOptionSelected && this.typeSelected){
        this.downloadReport();
        this.site.updateReportData(null, null);
      }
    });
  }

  receiveLiteDataFromHeader(){
    this.site.liteQnsAnsCnt$.subscribe(value => {
      this.liteQnsAnsCnt = value;
    });
  
    this.site.totalLiteQnsCnt$.subscribe(value => {
      this.totalLiteQnsCnt = value;
    });
  
    this.site.isLogicalQuestions$.subscribe(value => {
      this.isLogicalQuestions = value;
    });
  }

  checkIfSodexoLive(){
    if(this.site.seaSiteDetails.sites.segmentName.toLowerCase().includes("sodexo live")){
     return true;
    }else{
     return false;
    }
  }

  downloadReport(){ 
    if(this.reportOptionSelected == 1 && this.typeSelected == 'seaLite') {
      this.exportKpiCsv('seaLite');
      this.site.updateReportData(null, null);

      setTimeout(() => {
        this.popLoader = false;
        this.close1();
      }, 200);
    }
    else if(this.reportOptionSelected == 1 && this.typeSelected == 'seaFull') {
      this.exportKpiCsv('');
      this.site.updateReportData(null, null);

      setTimeout(() => {
        this.popLoader = false;
        this.close1();
      }, 200);
    }
    else if(this.reportOptionSelected == 2 && this.typeSelected == 'seaLite'){
      if(this.checkIfSodexoLive()){
        this.downloadFiles('liveLite');
        this.site.updateReportData(null, null);
  
        setTimeout(() => {
          this.popLoader = false;
          this.close1();
        }, 200);
      }else{
        this.downloadFiles('lite');
        this.site.updateReportData(null, null);
  
        setTimeout(() => {
          this.popLoader = false;
          this.close1();
        }, 200);
      }
    }
    else if(this.reportOptionSelected == 2 && this.typeSelected == 'seaFull'){
      if(this.checkIfSodexoLive()){
        this.downloadFiles('liveFull');
        this.site.updateReportData(null, null);
  
        setTimeout(() => {
          this.popLoader = false;
          this.close1();
        }, 200);
      }else{
        this.downloadFiles('full');
        this.site.updateReportData(null, null);
  
        setTimeout(() => {
          this.popLoader = false;
          this.close1();
        }, 200);
      }
    }
    else if(this.reportOptionSelected == 3 && this.typeSelected == 'seaLite') {
      this.site.liteQns$.subscribe(data =>{
        this.allLiteQns = data;
        this.exportAllDataExtractCsv('seaLite');

        this.reportOptionSelected = null;
        this.typeSelected = null;
        this.site.updateReportData(null, null);
      })

      setTimeout(() => {
        this.popLoader = false;
        this.close1();
      }, 200);
    }
    else if(this.reportOptionSelected == 3 && this.typeSelected == 'seaFull') {
      this.site.allQns$.subscribe(data =>{
        this.allQns = data;
        this.exportAllDataExtractCsv('seaFull');
        this.site.updateReportData(null, null);
      })

      setTimeout(() => {
        this.popLoader = false;
        this.close1();
      }, 200);
    }
  }

  ngAfterContentInit(): void {
    this.getPracticesQuestion();
  }

  getPracticesQuestion() {
    this.site.getBestPracticeForQuestion(this.siteCountry).subscribe((res: any) => {
      this.bestPracticesForQuestions = res;
    });
  }
  getViewData() {
    this.tooltipText = this.translate.instant("kFilter");

    const filterTopics = this.site.seaSiteDetails.siteScore.filter(
      (topic) => topic.value
    );
    this.tableViewData = filterTopics.map((val) => {
      val.children = val.children.filter((child) => child.value);
      try {
        val.language = JSON.parse(val.language);
      } catch (error) {
        val.language = val.language;
      }
      return val;
    });
  }

  getProfileEmail() {
    const siteId = this.route.snapshot.params.siteId;
    this.countryService.gocnsite(siteId).subscribe((res: any) => {
      this.email = res.siteProfile.email;
    }, (err: any) => {
      this.toaster.error('Unable to download, try again', 'Error!');
    });
  }

  getSiteStatus() {
    this.siteStatus = JSON.parse(localStorage.getItem("siteStatus"))
      ? true
      : false;
    if (this.siteStatus) {
      this.getViewData();
    } else {
      // this.dgls.unload();
    }
  }
  getSiteDeatailsData() {
    
    this.siteDetails = this.site.seaSiteDetails;
    this.siteCategoryId = this.siteDetails.sites.lite;    
    this.siteAddress =
    this.siteDetails.sites.Yourmainaddress || this.siteDetails.sites.address;
    this.SiteManagementData = this.siteDetails.sites.SiteManagement;
    this.consumerNumber = this.siteDetails.sites.consumerNumber;
    this.showGSA = this.siteDetails.sites.ila;
    this.profileLang = this.siteDetails.profile.languageIdentifier;
    this.parentTopic = this.siteDetails.siteScore;
    this.userDetails = this.siteDetails.sites;
    this.unit = this.userDetails.unit;
    let changeUnit = this.unit
    ? this.unit.includes("Square Feet (sq ft)")
    ? "sqft"
    : this.unit
    : "";
    this.unit = changeUnit;
    this.address = this.userDetails.Yourmainaddress;
    this.percentageTotalScore = Math.round(
      (this.userDetails.score / this.userDetails.maximumScore) * 100
    );
    this.percentageBenchmarkTotal = Math.round(
      (this.siteDetails.benchMarkScore / this.userDetails.maximumScore) * 100
    );
    this.maxScore = this.userDetails.maximumScore;
    try {
      this.SiteFilterData = JSON.parse(this.userDetails.area);
    } catch (e) {
      this.SiteFilterData = [];
    }

    const parsedAddress = JSON.parse(this.siteDetails?.sites?.address);
    this.city = parsedAddress?.city;
    this.country = parsedAddress?.country;

    // this.siteDetails.siteAreServices.map(val => {
    //   try {
    //     val.Childparentservice = JSON.parse(val.Childparentservice);
    //   } catch (e) {
    //     val.Childparentservice = val.Childparentservice;
    //   }
    //   try {
    //     val.discriptionLanguage = JSON.parse(val.discriptionLanguage);
    //   } catch (e) {
    //     val.discriptionLanguage = val.discriptionLanguage;
    //   }
    //   if (val.discriptionLanguage) {
    //     val.discription = val.discriptionLanguage[this.profileLang] ? val.discriptionLanguage[this.profileLang] : val.discriptionLanguage['en'];
    //   }
    //   val.isChooseFood = false;
    //   if (val.id && val.available) {
    //     val.isChooseFood = true;
    //   }
    // })
    this.userServices = this.siteDetails.siteAreServices.sort((a, b) => {
      return a.sequence - b.sequence;
    });
    this.siteId =
      this.route.snapshot.params.siteId || this.siteDetails.sites.id;
    this.displayedRows$ = of(this.parentTopic);
    if (this.siteStatus) {
      this.badgesQuestion();
      this.getServiceValue();
    } else {
      this.getServiceValue();
    }
  }
  showFilterData() {
    this.filterDat.forEach((fil: any) => {
      if (fil.name === "ila") {
        if (!this.showGSA) {
        } else {
          this.filter.push(fil);
        }
      } else {
        this.filter.push(fil);
      }
    });
  }
  goBackHome(e: any) {
    this.goGeneral.emit();
  }
  getServiceValue() {
    const modifiedServices = [];
    for (const item of this.userServices) {
      let disctiptionsLang = [];
      try {
        disctiptionsLang = JSON.parse(item.discriptionLanguage);
      } catch (e) {
        disctiptionsLang = item.discriptionLanguage;
      }
      try {
        item.language = JSON.parse(item.language);
      } catch (e) {
        item.language = item.language;
      }
      if (disctiptionsLang) {
        item.discription = disctiptionsLang[this.profileLang]
          ? disctiptionsLang[this.profileLang]
          : disctiptionsLang["en"];
      }
      if (!item.isChildService) {
        item.serviceName = item.serviceName || item.name;
        const filtDatas = (this.SiteFilterData || []).filter(
          (id: any) => id.id === item.services_services_services
        );
        item.unit = filtDatas.length ? filtDatas[0].unit : "";
        item.toggle = item.available == 1 ? true : false;
      } else {
        if (item.Childparentservice && item.Childparentservice.length) {
          item.serviceName = item.serviceName || item.name;
          const Childparentservice = item.Childparentservice.filter(
            (subservice) => item.id == subservice.id && subservice.available
          );
          item.toggle = Childparentservice.length == 1 ? true : false;
        } else {
          item.toggle = item.available == 1 ? true : false;
        }
      }

      item.langName = item?.language[this.profileLang]
        ? item?.language[this.profileLang]
        : item?.language["en"];
      modifiedServices.push(item);
    }
    // const isChildFilterData = modifiedServices.filter(child => child.isChildService)
    // modifiedServices.map(val => {
    //   if (!val.isChildService && val.Childparentservice && val.Childparentservice.length) {
    //     const filterChildActiveData = this.getActiveChildData(isChildFilterData, val.Childparentservice).then(res => {
    //     });
    //   }
    // });
    this.userServices = modifiedServices.filter(
      (val: any) => val.isChildService == false
    );
    if (this.userServices && this.userServices.length) {
      this.userServices.forEach((element) => {
        this.firstTotalInputValue = 0;
        this.secondTotalInputValue = 0;
        element.Childparentservice.forEach((ele) => {
          ele.kichenOnSite = ele.kichenOnSite ? true : false;
          if (ele.available) {
            element.available = true;
            element.toggle = true;
          }
          if (ele.available && ele.isareaUnit) {
            this.firstTotalInputValue += ele.areaInput1;
            this.secondTotalInputValue += ele.areaInput2;
          } else {
            this.firstTotalInputValue += 0;
            this.secondTotalInputValue += 0;
          }
        });
        element.firstTotalInputValue = this.firstTotalInputValue;
        element.secondTotalInputValue = this.secondTotalInputValue;
      });
    }
    const findFoodService = this.userServices;
    if(this.siteDetails.sites.isServicePopupOpen && this.isShowPopup){
      this.isShowPopup = false
      this.openSeaServiceModel( 1000);
    }else  if (findFoodService && findFoodService.length) {
      findFoodService.map((val: any) => {
        if (
          val.available &&
          val.Childparentservice &&
          val.Childparentservice.length
        ) {
          val.childServices = this.getChildServices(val.Childparentservice);
          const getAvailableChildData = val.Childparentservice.filter(
            (child: any) => child.available
          );
          if (val.available) {
            if (getAvailableChildData.length) {
              val.FoodSubService = false;
            } else {
              val.FoodSubService = true;
            }
          } else {
            val.FoodSubService = false;
          }
        } else {
          val.FoodSubService = false;
          val.childServices = "";
        }
      });
      const accepedSaveServices = findFoodService.filter(
        (ser) => ser.FoodSubService
      );
      this.fildFoodSubService = accepedSaveServices.length ? true : false;
      if (this.fildFoodSubService) {
        this.openSeaServiceModel(1);
      }
    } else {
      this.openSeaServiceModel(2);
    }
    if (
      !this.siteAddress ||
      !this.SiteManagementData ||
      (!this.consumerNumber && this.consumerNumber != 0) ||
      !this.siteStatus
    ) {
      this.openSeaServiceModel(3);
    }
  }
  getChildServices(childService) {
    var concatAllServices = "";
    childService.forEach((element, i) => {
      if (element.available) {
        const language = element.language[this.profileLang]
          ? element.language[this.profileLang]
          : element.language["en"];
        if (childService.length - 1 == i) {
          concatAllServices += language;
        } else {
          concatAllServices += language + "," + "\n";
        }
      }
    });
    return concatAllServices;
  }
  getActiveChildData(child, parentChild) {
    const resultData = [];
    return new Promise((resolve, reject) => {
      if (parentChild && parentChild.length) {
        for (const ch of parentChild) {
          const fildAvaible = child.filter(
            (val: any) => val.id == ch.id && val.available
          );
          if (fildAvaible.length) {
            ch.available = true;
            resultData.push(ch);
          } else {
            ch.available = false;
            resultData.push(ch);
          }
        }
        resolve(resultData);
      } else {
        resolve(resultData);
      }
    });
  }
  openSeaServiceModel(renderNumber) {
    const serviceModel = this.dialog.open(SeaServiceComponent, {
      disableClose: true,
      hasBackdrop: true,
      width: "91%",
      height: "90%",
      panelClass: "SeaServiceModel",
      maxWidth: "140vw",
    });
    serviceModel.componentInstance.profileLang = this.profileLang;
    serviceModel.componentInstance.userServices = this.userServices;
    serviceModel.componentInstance.services = this.siteDetails.siteAreServices;
    serviceModel.componentInstance.siteId = this.siteId;
    serviceModel.componentInstance.unit = this.unit;
    serviceModel.componentInstance.address = this.address;
    serviceModel.componentInstance.allowedFoodSubService =
      this.fildFoodSubService;
    // serviceModel.componentInstance.siteManagementData = this.siteManagementDetails;
    serviceModel.componentInstance.siteManagementValue = this.siteManagement;
    serviceModel.componentInstance.renderNumber = (renderNumber || 0);

    serviceModel.afterClosed().subscribe((result) => {
      this.getSiteDeatailsData();
    });
  }
  filterResult() {
    this.filterResul = !this.filterResul;
    this.isRotated = !this.isRotated;
  }
  bestPracticeModel() {
    const bestPractice = this.dialog.open(BestPracticeLibraryComponent, {
      disableClose: true,
      hasBackdrop: true,
      minWidth: "60%",
      minHeight: "60vh",
      panelClass: "bestPracticeLibLogFile",
      maxWidth: "100%",
      maxHeight: "70vh",
    });
    bestPractice.componentInstance.siteDetails = this.siteDetails;
  }
  changeColor(index) {
    this.cColor = index;
  }
  getViewFilterApi(filterData: any) {
    const siteId = this.siteDetails.sites.id;
    // this.spinner.show();
    this.dgls.load();
    this.site.getSiteRecordByJson(siteId, filterData.name).subscribe(
      (data: any) => {
        const siteScoreChartData = data.siteScore;
        const siteAnsweredData = data.answered;
        this.site
          .getScoreDetails(siteId, siteScoreChartData, siteAnsweredData)
          .subscribe((getScore: any) => {
            data.sites = { ...data.sites, ...getScore.sitesData };
            this.site.seaSiteDetails = data;
            this.site.seaSiteDetails = { ...data, ...getScore };
            this.site.managerDetails = data.profile;
            this.filterResul = false;
            this.getSiteDeatailsData();
            this.getViewData();
            this.dgls.unload();
          });
      },
      (err: any) => {
        this.spinner.hide();
        // this.ts.error('Site not found');
      }
    );
  }
  seaDownloadReports(index: any) {
    this.downloadFile = index;
  }
  downloadFiles(type) {
    this.hideStyle = true;
    this.dgls.load();
    if(type == 'lite'){
      this.PptFileDownload.seaLitePPtDownload(
        this.siteDetails,
        this.badgesAllQuestion,
        this.getKPIPerformanceTopic,
        this.implemented,
        this.notImplemented
      )
        .then((res) => {
          if (res) {
            this.dgls.unload();
          }
        })
        .catch((e) => {
          this.spinner.hide();
        });
    }
    else if(type == 'liveLite'){
      this.PptFileDownload.seaLiveLitePPtDownload(
        this.siteDetails,
        this.badgesAllQuestion,
        this.getKPIPerformanceTopic,
        this.implemented,
        this.notImplemented
      )
        .then((res) => {
          if (res) {
            this.dgls.unload();
          }
        })
        .catch((e) => {
          this.spinner.hide();
        });
    }

    
    else if(type == 'full'){
      this.PptFileDownload.seaPPtDownload(
        this.siteDetails,
        this.badgesAllQuestion,
        this.getKPIPerformanceTopic
      )
        .then((res) => {
          if (res) {
            this.dgls.unload();
          }
        })
        .catch((e) => {
          this.spinner.hide();
        });
    }
    else if(type == 'liveFull'){
      this.PptFileDownload.seaLiveFullPPtDownload(
        this.siteDetails,
        this.badgesAllQuestion,
        this.getKPIPerformanceTopic
      )
        .then((res) => {
          if (res) {
            this.dgls.unload();
          }
        })
        .catch((e) => {
          this.spinner.hide();
        });
    }
  }
  csvOrPptPopup() {
    this.reportOption = null;
    this.dialogRefCSVOrPpt = this.modalService.open(this.staticCsvPop, {
      centered: true,
    });
  }

  // async submitDownloadFiles() {
  //   this.popLoader = true;
  //   if (this.reportOption === 1) {
  //     this.exportKpiCsv('');

  //     setTimeout(() => {
  //       this.popLoader = false;
  //       this.close1();
  //     }, 200);
  //   } else if (this.reportOption === 2) {
  //     this.downloadFiles();

  //     setTimeout(() => {
  //       this.popLoader = false;
  //       this.close1();
  //     }, 200);
  //   }
  // }

  findAnswer(qnToBeParsed, obj) {
    if (qnToBeParsed) {
      const parsedArr = JSON.parse(qnToBeParsed);

      if (obj.questionType.includes('slider')) {
        const idx = parsedArr.findIndex(item => item?.isAnswer);
        if (idx !== -1) {
          return parsedArr[idx]?.lang?.options?.[idx]?.name || "";
        }
        return "";
      } 
      
      else if (obj.questionType.includes('numeric')) {
        const idx = parsedArr.findIndex(item => item?.value);
        if (parsedArr[idx]?.value) {
          return parsedArr[idx]?.value || "";
        }
        return "";
      }
      
      else{
        const answerObj = parsedArr.find(obj => obj.value === true || obj.value.length > 0);
    
        if (answerObj) {
          if (answerObj.value === true) {
            return answerObj.name;  // If value is true, return the name
          } else {
            return answerObj.value; // If value is a non-empty string, return the value
          }
        }
        return ""; // If no match is found
      }
    } else {
      return "";
    }
  }

  findKPIPercentageForCategory(category, topic) {
    const categoryTopicPerformance = this.badgesAllQuestion[category][topic]?.performance;

    if (!categoryTopicPerformance || categoryTopicPerformance.includes("low")) {
      return "< 35%";
    }

    else if (categoryTopicPerformance.includes("average")) {
      return "> 35% < 75%";
    }

    else if (categoryTopicPerformance.includes("high")) {
      return "> 75%";
    }
  }

  extractKPIData(){
    this.site.getbadgesQuestions(this.siteDetails.sites.id).subscribe((res: any) => {
      const result = {};
      for (const key in res) {
        if (res.hasOwnProperty(key) && res[key] && Array.isArray(res[key])) {
          const category = key;

          const sodexoEssentialsKey = `${category[0].toUpperCase()}${category.slice(1)}_Sodexo Essentials`;
          const measurementsKey = `${category[0].toUpperCase()}${category.slice(1)}_Measurements`;
          const collaborativeActionsKey = `${category[0].toUpperCase()}${category.slice(1)}_Collaborative Actions`;

          const sodexoEssentialsArray = [];
          const measurementsArray = [];
          const collaborativeActionsArray = [];

          res[category].forEach((item) => {
            if (item.sodexoEssentials === 1) {
              sodexoEssentialsArray.push(item);
            }
            if (item.measurements === 1) {
              measurementsArray.push(item);
            }
            if (item.collaborativeActions === 1) {
              collaborativeActionsArray.push(item);
            }
          });

          result[sodexoEssentialsKey] = sodexoEssentialsArray;
          result[collaborativeActionsKey] = collaborativeActionsArray;
          result[measurementsKey] = measurementsArray;
        }
      }
      const bestPracticeObj = {};
      this.bestPracticesForQuestions.forEach((obj) => {
        bestPracticeObj[obj.id] = { name: obj.name, imageUrl: obj.imageUrl, score: obj.score, language: obj.language };
      });
 
      let data = [];
      for (const category in result) {
        result[category].forEach((obj) => {
          const payload = {
            'SEA Topic': category.split("_")[1],
            'SEA Category': category.split("_")[0],
            'Performance Results %': this.findKPIPercentageForCategory(category.split("_")[0], category.split("_")[1]) || '-',
            'Performance Action': obj.content,
            'Response Recorded SEA': this.findAnswer(obj.answer, obj) || '-',
            'Site': this.userDetails?.name || '-',
            'Applicable Sodexo Best Practice': bestPracticeObj[obj.id]?.name 
            ? { text: bestPracticeObj[obj.id]?.name, hyperlink: bestPracticeObj[obj.id]?.imageUrl,  score: bestPracticeObj[obj.id]?.score,  language: bestPracticeObj[obj.id]?.language }
            : '-',     
          };

          data.push(payload);
        });
      }

      const performanceMap = {
        "-": 0,
        "< 35%": 1,
        "> 35% < 75%": 2,
        "> 75%": 3,
      };

      data.sort((a: any, b: any) => {
        return performanceMap[a["Performance Results %"]] - performanceMap[b["Performance Results %"]];
      });

      this.excelData = data;

      let implemented = [];
      let notImplemented = [];

      this.excelData.forEach(obj => {
        if(obj['Performance Results %'] == '> 75%'){
          if(obj['Applicable Sodexo Best Practice'].text){
            implemented.push({
              name: obj['Applicable Sodexo Best Practice'].text,
              imageUrl: obj['Applicable Sodexo Best Practice'].hyperlink,
              score: obj['Applicable Sodexo Best Practice'].score,
              language: obj['Applicable Sodexo Best Practice'].language
            })
          }
        }else if(obj['Performance Results %'] == '> 35% < 75%' || obj['Performance Results %'] == '< 35%'){
          if(obj['Applicable Sodexo Best Practice'].text){
            notImplemented.push({
              name: obj['Applicable Sodexo Best Practice'].text,
              imageUrl: obj['Applicable Sodexo Best Practice'].hyperlink,
              score: obj['Applicable Sodexo Best Practice'].score,
              language: obj['Applicable Sodexo Best Practice'].language
            })
          }
        }
      })

      this.implemented = implemented;
      this.notImplemented = notImplemented; 
    })
  }
  exportKpiCsv(type) {
    console.log('🚀 exportKpiCsv started with type:', type);

    this.site.getbadgesQuestions(this.siteDetails.sites.id).subscribe((res: any) => {
      console.log('📊 Raw API Response:', res);

      const result = {};
      console.log('🔄 Starting to process categories...');

      for (const key in res) {
        if (res.hasOwnProperty(key) && res[key] && Array.isArray(res[key])) {
          const category = key;
          console.log(`📂 Processing category: ${category}`);

          const sodexoEssentialsKey = `${category[0].toUpperCase()}${category.slice(1)}_Sodexo Essentials`;
          const measurementsKey = `${category[0].toUpperCase()}${category.slice(1)}_Measurements`;
          const collaborativeActionsKey = `${category[0].toUpperCase()}${category.slice(1)}_Collaborative Actions`;

          console.log(`🔑 Generated keys for ${category}:`, {
            sodexoEssentialsKey,
            measurementsKey,
            collaborativeActionsKey
          });

          const sodexoEssentialsArray = [];
          const measurementsArray = [];
          const collaborativeActionsArray = [];

          res[category].forEach((item, index) => {
            console.log(`📋 Processing item ${index} in ${category}:`, item);

            if (item.sodexoEssentials === 1) {
              sodexoEssentialsArray.push(item);
              console.log(`✅ Added to sodexoEssentials: ${item.id || 'no-id'}`);
            }
            if (item.measurements === 1) {
              measurementsArray.push(item);
              console.log(`✅ Added to measurements: ${item.id || 'no-id'}`);
            }
            if (item.collaborativeActions === 1) {
              collaborativeActionsArray.push(item);
              console.log(`✅ Added to collaborativeActions: ${item.id || 'no-id'}`);
            }
          });

          result[sodexoEssentialsKey] = sodexoEssentialsArray;
          result[collaborativeActionsKey] = collaborativeActionsArray;
          result[measurementsKey] = measurementsArray;

          console.log(`📊 Arrays for ${category}:`, {
            sodexoEssentials: sodexoEssentialsArray.length,
            measurements: measurementsArray.length,
            collaborativeActions: collaborativeActionsArray.length
          });
        }
      }

      console.log('🏗️ Final result object:', result);

      const bestPracticeObj = {};
      console.log('🎯 Processing best practices:', this.bestPracticesForQuestions);

      this.bestPracticesForQuestions.forEach((obj) => {
        bestPracticeObj[obj.id] = { name: obj.name, imageUrl: obj.imageUrl };
      });

      console.log('🎯 Best practice object created:', bestPracticeObj);

      let data = [];
      console.log('🔄 Starting payload creation for each category...');

      for (const category in result) {
        console.log(`\n📂 Processing category for payload: ${category}`);
        console.log(`📊 Items in this category: ${result[category].length}`);

        result[category].forEach((obj, index) => {
          console.log(`\n🔍 Processing item ${index} in ${category}:`, obj);

          let parsedArr;
          try {
            parsedArr = JSON.parse(obj.answer);
            console.log('✅ Parsed answer:', parsedArr);
          } catch (e) {
            console.log('❌ Failed to parse answer:', obj.answer, 'Error:', e);
            parsedArr = obj.answer;
          }

          // Log individual payload components
          const seaTopic = category.split("_")[1];
          const seaCategory = category.split("_")[0];
          const performanceResults = this.findKPIPercentageForCategory(category.split("_")[0], category.split("_")[1]) || '-';
          const performanceAction = obj.language[this.profileLang]?.content ? obj.language[this.profileLang].content : obj.content;

          console.log('🏷️ Payload components:', {
            seaTopic,
            seaCategory,
            performanceResults,
            performanceAction: performanceAction?.substring(0, 50) + '...',
            questionType: obj.questionType,
            profileLang: this.profileLang
          });

          // Determine Response Recorded SEA
          let responseRecordedSEA;
          if (parsedArr && obj.questionType?.includes('file upload')) {
            responseRecordedSEA = { text: parsedArr[0].value, hyperlink: parsedArr[0].value };
            console.log('📁 File upload response:', responseRecordedSEA);
          } else if (obj.questionType === 'info' && obj.language?.[this.profileLang]?.options?.[0]?.name) {
            responseRecordedSEA = obj.language[this.profileLang].options[0].name;
            console.log('ℹ️ Info response:', responseRecordedSEA);
          } else {
            responseRecordedSEA = this.findAnswer(obj.answer, obj) || '-';
            console.log('📝 Standard response:', responseRecordedSEA);
          }

          const payload = {
            'SEA Topic': seaTopic,
            'SEA Category': seaCategory,
            'Performance Results %': performanceResults,
            'Performance Action': performanceAction,
            'Response Recorded SEA': responseRecordedSEA,
            'Site': this.userDetails?.name || '-',
            'Applicable Sodexo Best Practice': bestPracticeObj[obj.id]?.name
            ? { text: bestPracticeObj[obj.id]?.name, hyperlink: bestPracticeObj[obj.id]?.imageUrl }
            : '-',
          };

          console.log(`✨ Created payload for item ${index}:`, payload);
          data.push(payload);
          console.log(`📊 Total payloads so far: ${data.length}`);
        });
      }

      console.log('🎉 All payloads created. Total count:', data.length);
      console.log('📋 Complete data array:', data);

      const performanceMap = {
        "-": 0,
        "< 35%": 1,
        "> 35% < 75%": 2,
        "> 75%": 3,
      };

      if(type == 'seaLite'){
        data = data.filter(obj => obj['SEA Topic'] == 'Sodexo Essentials');
      }

      data.sort((a: any, b: any) => {
        return performanceMap[a["Performance Results %"]] - performanceMap[b["Performance Results %"]];
      });
 
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('KPI Report');

      // Add headers with styles
      worksheet.columns = [
        { header: 'SEA CATEGORY', key: 'SEA Category', width: 20 },
        { header: 'SEA KPI', key: 'SEA Topic', width: 20 },
        { header: 'PERFORMANCE RESULTS %', key: 'Performance Results %', width: 20 },
        { header: 'PERFORMANCE ACTION', key: 'Performance Action', width: 50 },
        { header: 'SEA RESPONSE', key: 'Response Recorded SEA', width: 30 },
        { header: 'SITE NAME', key: 'Site', width: 15 },
        { header: 'PARENT SERVICE', key: 'Responcible Service', width: 10 },
        { header: 'ACTION OWNER', key: 'Action Owner', width: 10 },
        { header: 'APPLICABLE SODEXO BEST PRACTICE', key: 'Applicable Sodexo Best Practice', width: 50 },  
        { header: 'SPECIFICATIONS OR MITIGATING FACTORS', key: 'SPECIFICATIONS OR MITIGATING FACTORS', width: 30 },
        { header: 'IS THERE A PLAN TO ADDRESS THE ACTION? Y/N', key: 'IS THERE A PLAN TO ADDRESS THE ACTION? Y/N', width: 30 },
        { header: 'PROPOSED ACTION', key: 'PROPOSED ACTION', width: 30 },
        { header: 'TECHNICAL RECOMMENDATION', key: 'TECHNICAL RECOMMENDATION', width: 30 },
        { header: 'POSSIBLE ALTERNATIVE', key: 'POSSIBLE ALTERNATIVE', width: 30 },
        { header: 'ALTERNATIVE PROPOSED ACTION', key: 'ALTERNATIVE PROPOSED ACTION', width: 30 },
        { header: 'ALTERNATIVE TECHNICAL RECOMMENDATION', key: 'ALTERNATIVE TECHNICAL RECOMMENDATION', width: 30 },
        { header: 'INITIAL ESTIMATED COST', key: 'Initial Estimated Cost', width: 20 },
        { header: 'HAS THE ESTIMATED COST BEEN REVISED', key: 'Has the Estimated Cost been revised?', width: 30 },
        { header: 'REVISED COST', key: 'Revised Cost', width: 20 },
        { header: 'TOTAL COST PER ACTION', key: 'Total Cost per Action', width: 20 },
        { header: 'START MONTH', key: 'Start Month', width: 15 },
        { header: 'END MONTH', key: 'End Month', width: 15 },
        { header: 'COMMENTS / NOTES (OPTIONAL)', key: 'Comments / Notes(Optional)', width: 30 },
      ];


      // Style header row
      worksheet.getRow(1).font = {
        name: 'Arial',
        size: 12,
        bold: true,
        color: { argb: '000000' }, // Black color
      };

      // Optionally, center-align the headers
      worksheet.getRow(1).alignment = { horizontal: 'center' };

      // Add data and apply conditional formatting
      data.forEach((row, index) => {
        const addedRow = worksheet.addRow(row);

        const performanceValue = row['Performance Results %'];
        let bgColor = 'FF0000';

        if (performanceValue === '< 35%') {
          bgColor = 'FF0000';
        } else if (performanceValue === '> 35% < 75%') {
          bgColor = 'FFA500';
        } else if (performanceValue === '> 75%') {
          bgColor = '90EE90';
        }

        // Apply background color to the "Performance Results %" column
        if (bgColor) {
          addedRow.getCell(3).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: bgColor },
          };
        }

        // Apply hyperlink and blue font color for info / file upload type answers because they are links
        if (
          (typeof row['Response Recorded SEA'] === 'string' && row['Response Recorded SEA'].startsWith('http')) ||
          (typeof row['Response Recorded SEA'] === 'object' && typeof row['Response Recorded SEA']?.text === 'string' && row['Response Recorded SEA']?.text.startsWith('http'))
        ) {
          const responseCell = addedRow.getCell(5);
          responseCell.font = {
            color: { argb: '0000FF' },
            underline: true
          };
        }
                
        // Apply hyperlink and blue font color
        const bestPracticeCell = addedRow.getCell(9);
        bestPracticeCell.font = {
          color: { argb: '0000FF' },
        };
      });

      // workbook.xlsx.writeBuffer().then((buffer) => {
      //   const blob = new Blob([buffer], { type: 'application/octet-stream' });
      //   const link = document.createElement('a');
      //   link.href = window.URL.createObjectURL(blob);
      //   link.download = 'SEA_KPI_Performance_Report.xlsx';
      //   link.click();
      
      //   // Prepare the payload for the API call
      //   const payload = {
      //     data: buffer,
      //     name: `${this.siteDetails.sites.name}-sea_csv-${new Date().toISOString()}.xlsx`,  // Using the site name and current date for file naming
      //     type: 'sodexocsv',
      //     siteDetails: this.site.seaSiteDetails.sites,
      //     downloadedBy: this.email  // Assuming `this.email` holds the email of the user
      //   };
      
      //   // Make the API call to upload the file
      //   this.lts.createXLSXUpload(payload).subscribe((res: any) => {
      //     this.toaster.success('File uploaded Successfully')
      //   });
      
      //     this.spinner.hide();
      //   });
      });

  }

  exportAllDataExtractCsv(type) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('All Data Extract');

      // First set of columns (main data)
      worksheet.columns = [
        { header: 'SITE ID', key: 'siteId', width: 15 },
        { header: 'SITE NAME', key: 'siteName', width: 15 },
        { header: 'SEA CATEGORY', key: 'topic', width: 20 },
        { header: 'SEA QUESTION', key: 'translatedQn', width: 50 },
        { header: 'SEA RESPONSE', key: 'translatedAns', width: 30 }
      ];

      // Style header row
      worksheet.getRow(1).font = {
        name: 'Arial',
        size: 12,
        bold: true,
        color: { argb: '000000' }, // Black color
      };

      worksheet.getRow(1).alignment = { horizontal: 'center' };

      // Get site ID from siteDetails
      const siteId = this.siteDetails?.sites?.id || this.site?.seaSiteDetails?.sites?.id || '';

      if(type.includes('seaLite')){
        this.allLiteQns.forEach((row, index) => {
          // If questionType is 'file upload', make translatedAns a hyperlink
          if(row.questionType === 'file upload' && row.translatedAns) {
            const newRow = worksheet.addRow({
              topic: row.topic,
              translatedQn: row.translatedQn,
              translatedAns: { text: row.translatedAns, hyperlink: row.translatedAns }, 
              siteName: row.siteName,
              siteId: siteId
            });
            const responseCell = newRow.getCell(5);
            responseCell.font = {
              color: { argb: '0000FF' },
              underline: true
            };
          }  
         else if(row.questionType === 'info' && row.answer.length) {
            const newRow = worksheet.addRow({
              topic: row.topic,
              translatedQn: row.translatedQn,
              translatedAns: { text: row.answer[0]?.name, hyperlink: row.answer[0]?.name }, 
              siteName: row.siteName,
              siteId: siteId
            });
            const responseCell = newRow.getCell(5);
            responseCell.font = {
              color: { argb: '0000FF' },
              underline: true
            };
          } 
          else {
            // Add siteId to the row before adding it
            row.siteId = siteId;
            worksheet.addRow(row);
          }
        });
      }
      else if(type.includes('seaFull')){
        this.allQns.forEach((row, index) => {
          // If questionType is 'file upload', make translatedAns a hyperlink
          if(row.questionType === 'file upload' && row.translatedAns) {
            const newRow = worksheet.addRow({
              topic: row.topic,
              translatedQn: row.translatedQn,
              translatedAns: { text: row.translatedAns, hyperlink: row.translatedAns }, 
              siteName: row.siteName,
              siteId: siteId
            });
            const responseCell = newRow.getCell(5);
            responseCell.font = {
              color: { argb: '0000FF' },
              underline: true
            };
          }
          else if(row.questionType === 'info' && row.answer.length) {
            const newRow = worksheet.addRow({
              topic: row.topic,
              translatedQn: row.translatedQn,
              translatedAns: { text: row.answer[0]?.name, hyperlink: row.answer[0]?.name }, 
              siteName: row.siteName,
              siteId: siteId
            });
            const responseCell = newRow.getCell(5);
            responseCell.font = {
              color: { argb: '0000FF' },
              underline: true
            };
          } 
          else {
            // Add siteId to the row before adding it
            row.siteId = siteId;
            worksheet.addRow(row);
          }
        });
      }

      const extractedOnText = this.translate.instant('kExtractedOn');
      worksheet.addRow(['']);
      const lastRow = worksheet.addRow(['', '', extractedOnText + ':', new Date().toLocaleDateString()]);
      lastRow.getCell(3).font = { bold: true };
      
      // Add a blank row
      worksheet.addRow(['']);
      
      // Add metadata headers in the same format as the main headers
      worksheet.addRow([
        { header: 'Site Lead email', key: 'siteLeadEmail', width: 30 },
        { header: 'Please select your services', key: 'services', width: 40 },
        { header: 'Created By', key: 'createdBy', width: 20 },
        { header: 'Created', key: 'created', width: 15 },
        { header: 'Modified By', key: 'modifiedBy', width: 20 },
        { header: 'Modified', key: 'modified', width: 30 },
        { header: 'Language', key: 'language', width: 30 }
      ].map(col => col.header));
      
      // Style the metadata header row
      const metadataRowIndex = worksheet.rowCount;
      const metadataRow = worksheet.getRow(metadataRowIndex);
      metadataRow.font = {
        name: 'Arial',
        size: 12,
        bold: true,
        color: { argb: '000000' }, // Black color
      };
      
      metadataRow.alignment = { horizontal: 'center' };
      
      // Continue with the rest of your code
      workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], { type: 'application/octet-stream' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = 'SEA_All_Data_Extract_Report.xlsx';
        link.click();
      
        // Prepare the payload for the API call
        const payload = {
          data: buffer,
          name: `${this.siteDetails.sites.name}-sea_csv-${new Date().toISOString()}.xlsx`,  // Using the site name and current date for file naming
          type: 'sodexocsv',
          siteDetails: this.site.seaSiteDetails.sites,
          downloadedBy: this.email  // Assuming `this.email` holds the email of the user
        };
      
        // Make the API call to upload the file
        this.lts.createXLSXUpload(payload).subscribe((res: any) => {
          console.log('res',res);
          this.toaster.success('File uploaded Successfully')
        });
      
          this.spinner.hide();
      });
  }

  close1() {
    this.popDisabled = true;
    // this.dialogRefCSVOrPpt.close();
  }
  selectOption() {
    if (this.reportOption) {
      this.popDisabled = false;
    }
  }
  badgesQuestion() {
    this.performance = {
      "Energy Management": {
        sodexoEssentialsCount: 0,
        measurementsCount: 0,
        collaborativeActions: 0,
      },
      "Waste Management": {
        sodexoEssentialsCount: 0,
        measurementsCount: 0,
        collaborativeActions: 0,
      },
      "Water Management": {
        sodexoEssentialsCount: 0,
        measurementsCount: 0,
        collaborativeActions: 0,
      },
      "Sustainable Eating": {
        sodexoEssentialsCount: 0,
        measurementsCount: 0,
        collaborativeActions: 0,
      },
      "General Initiatives": {
        sodexoEssentialsCount: 0,
        measurementsCount: 0,
        collaborativeActions: 0,
      },
    };
    this.site.getbadgesQuestions(this.site.siteId).subscribe((res: any) => {
      if (res["Energy Management"] && res["Energy Management"].length) {
        this.getKPIPerformanceTopic.push("Energy Management");
      }
      if (res["Waste Management"] && res["Waste Management"].length) {
        this.getKPIPerformanceTopic.push("Waste Management");
      }
      if (res["Water Management"] && res["Water Management"].length) {
        this.getKPIPerformanceTopic.push("Water Management");
      }
      this.performance.collaborativeEnergyPass = {
        count: res["Energy Management"].filter(
          (item) => item.collaborativeActions == 1
        ).length,
        score: res.collaborativeEnergyPass,
        performance: this.calculatePerformance(
          res.collaborativeEnergyPass,
          res["Energy Management"].filter(
            (item) => item.collaborativeActions == 1
          ).length
        ),
      };
      this.performance.measurementEnergyPass = {
        count: res["Energy Management"].filter((item) => item.measurements == 1)
          .length,
        score: res.measurementEnergyPass,
        performance: this.calculatePerformance(
          res.measurementEnergyPass,
          res["Energy Management"].filter((item) => item.measurements == 1)
            .length
        ),
      };
      this.performance.sodexoEssEnergyPass = {
        count: res["Energy Management"].filter(
          (item) => item.sodexoEssentials == 1
        ).length,
        score: res.sodexoEssEnergyPass,
        performance: this.calculatePerformance(
          res.sodexoEssEnergyPass,
          res["Energy Management"].filter((item) => item.sodexoEssentials == 1)
            .length
        ),
      };

      this.performance.collaborativeWastePass = {
        count: res["Waste Management"].filter(
          (item) => item.collaborativeActions == 1
        ).length,
        score: res.collaborativeWastePass,
        performance: this.calculatePerformance(
          res.collaborativeWastePass,
          res["Waste Management"].filter(
            (item) => item.collaborativeActions == 1
          ).length
        ),
      };
      this.performance.measurementWastePass = {
        count: res["Waste Management"].filter((item) => item.measurements == 1)
          .length,
        score: res.measurementWastePass,
        performance: this.calculatePerformance(
          res.measurementWastePass,
          res["Waste Management"].filter((item) => item.measurements == 1)
            .length
        ),
      };
      this.performance.collaborativeWaterPass = {
        count: res["Water Management"].filter(
          (item) => item.collaborativeActions == 1
        ).length,
        score: res.collaborativeWaterPass,
        performance: this.calculatePerformance(
          res.collaborativeWaterPass,
          res["Water Management"].filter(
            (item) => item.collaborativeActions == 1
          ).length
        ),
      };

      this.performance.measurementWaterPass = {
        count: res["Water Management"].filter((item) => item.measurements == 1)
          .length,
        score: res.measurementWaterPass,
        performance: this.calculatePerformance(
          res.measurementWaterPass,
          res["Water Management"].filter((item) => item.measurements == 1)
            .length
        ),
      };

      this.performance.sodexoEssWastePass = {
        count: res["Waste Management"].filter(
          (item) => item.sodexoEssentials == 1
        ).length,
        score: res.sodexoEssWastePass,
        performance: this.calculatePerformance(
          res.sodexoEssWastePass,
          res["Waste Management"].filter((item) => item.sodexoEssentials == 1)
            .length
        ),
      };
      this.performance.sodexoEssWaterPass = {
        count: res["Water Management"].filter(
          (item) => item.sodexoEssentials == 1
        ).length,
        score: res.sodexoEssWaterPass,
        performance: this.calculatePerformance(
          res.sodexoEssWaterPass,
          res["Water Management"].filter((item) => item.sodexoEssentials == 1)
            .length
        ),
      };
      if (res["Sustainable Eating"]) {
        this.getKPIPerformanceTopic.push("Sustainable Eating");
        this.performance.collaborativeSustainablePass = {
          count: res["Sustainable Eating"].filter(
            (item) => item.collaborativeActions == 1
          ).length,
          score: res.collaborativeSustainablePass,
          performance: this.calculatePerformance(
            res.collaborativeSustainablePass,
            res["Sustainable Eating"].filter(
              (item) => item.collaborativeActions == 1
            ).length
          ),
        };
        this.performance.measurementSustainablePass = {
          count: res["Sustainable Eating"].filter(
            (item) => item.measurements == 1
          ).length,
          score: res.measurementSustainablePass,
          performance: this.calculatePerformance(
            res.measurementSustainablePass,
            res["Sustainable Eating"].filter((item) => item.measurements == 1)
              .length
          ),
        };
        this.performance.sodexoEssSustainablePass = {
          count: res["Sustainable Eating"].filter(
            (item) => item.sodexoEssentials == 1
          ).length,
          score: res.sodexoEssSustainablePass,
          performance: this.calculatePerformance(
            res.sodexoEssSustainablePass,
            res["Sustainable Eating"].filter(
              (item) => item.sodexoEssentials == 1
            ).length
          ),
        };
      }
      // if (res["General Initiatives"]) {
      //   this.getKPIPerformanceTopic.push("General Initiatives");
      //   this.performance.collaborativeSustainablePass = {
      //     count: res["General Initiatives"].filter(
      //       (item) => item.collaborativeActions == 1
      //     ).length,
      //     score: res.collaborativeSustainablePass,
      //     performance: this.calculatePerformance(
      //       res.collaborativeSustainablePass,
      //       res["General Initiatives"].filter(
      //         (item) => item.collaborativeActions == 1
      //       ).length
      //     ),
      //   };
      //   this.performance.measurementSustainablePass = {
      //     count: res["General Initiatives"].filter(
      //       (item) => item.measurements == 1
      //     ).length,
      //     score: res.measurementSustainablePass,
      //     performance: this.calculatePerformance(
      //       res.measurementSustainablePass,
      //       res["General Initiatives"].filter((item) => item.measurements == 1)
      //         .length
      //     ),
      //   };
      //   this.performance.sodexoEssSustainablePass = {
      //     count: res["General Initiatives"].filter(
      //       (item) => item.sodexoEssentials == 1
      //     ).length,
      //     score: res.sodexoEssSustainablePass,
      //     performance: this.calculatePerformance(
      //       res.sodexoEssSustainablePass,
      //       res["General Initiatives"].filter(
      //         (item) => item.sodexoEssentials == 1
      //       ).length
      //     ),
      //   };
      // }

      this.badgesAllQuestion = {
        "Energy Management": {
          "Sodexo Essentials": this.performance.sodexoEssEnergyPass,
          "Collaborative Actions": this.performance.collaborativeEnergyPass,
          Measurements: this.performance.measurementEnergyPass,
        },
        "Waste Management": {
          "Sodexo Essentials": this.performance.sodexoEssWastePass,
          "Collaborative Actions": this.performance.collaborativeWastePass,
          Measurements: this.performance.measurementWastePass,
        },
        "Water Management": {
          "Sodexo Essentials": this.performance.sodexoEssWaterPass,
          "Collaborative Actions": this.performance.collaborativeWaterPass,
          Measurements: this.performance.measurementWaterPass,
        },
        "Sustainable Eating": {
          "Sodexo Essentials": this.performance.sodexoEssSustainablePass,
          "Collaborative Actions":
            this.performance.collaborativeSustainablePass,
          Measurements: this.performance.measurementSustainablePass,
        },
        "General Initiatives": {
          "Sodexo Essentials": this.performance.sodexoEssSustainablePass,
          "Collaborative Actions":
            this.performance.collaborativeSustainablePass,
          Measurements: this.performance.measurementSustainablePass,
        },
      };
    });
  }
  calculatePerformance(score, total) {
    const percentage = (score / total) * 100;
    if (score == 0 || percentage <= 35) {
      return "low";
    } else if (percentage > 35 && percentage < 75) {
      return "average";
    } else {
      return "high";
    }
  }
  goToPerformanceDashboard() {
    this.dgls.load();
    sessionStorage.setItem(
      "languageCode",
      this.siteDetails.profile.languageIdentifier
    );
    sessionStorage.setItem("sites", this.siteDetails.profile.sites.id);
    sessionStorage.setItem("email", this.siteDetails.profile.email);
    this.siteId =
      this.route.snapshot.params.siteId || this.siteDetails.sites.id;
    this.router.navigate([`site/${this.siteId}/dashboard`]);
  }
  // getSiteManagementDetails() {
  //   this.SiteManagementService.getSiteManagementList().subscribe((res: any) => {
  //     res.map(val => {
  //       val.managementName = val.language[this.profileLang] ? val.language[this.profileLang] : val.language['en'];
  //     });
  //     this.siteManagementDetails = [...res];
  //   });
  // }
  // getSiteDetails() {
  //   this.siteService.getRecordById(this.userDetails.id).subscribe((res: any) => {
  //     res.SiteManagement.managementName = res.SiteManagement.language[this.profileLang] ? res.SiteManagement.language[this.profileLang] : res.SiteManagement.language['en']
  //     this.siteManagement = res.SiteManagement;
  //   });
  // }

  openSustainabilityPerformance(){
    this.isPerformanceClicked = true;
  }

  openMyBestPractices(){
    this.isPerformanceClicked = false;
  }
  openAllQuestions(type){
    const dialogRef2 = this.dialogRef = this.dialog.open(SeaLiteQuestionsComponent, {
      disableClose: true,
      hasBackdrop: true,
      height: '75%',
      width: '70%',
      panelClass: 'QuestionModel',
      maxHeight: '100%',
      maxWidth: '100%',
    });   

    dialogRef2.componentInstance.isOpenLiteQns = true;
    dialogRef2.componentInstance.liteQnsAnsCnt = this.liteQnsAnsCnt;
    dialogRef2.componentInstance.totalLiteQnsCnt = this.totalLiteQnsCnt;
    dialogRef2.componentInstance.isLogicalQuestions = this.isLogicalQuestions;
  }
}

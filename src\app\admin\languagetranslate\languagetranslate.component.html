<div class="row">
    <div class="col-lg-2 question-text">
        <h2 style="color: rgb(42,41,92);">Field's Translate</h2>
    </div>
    <div class="col-lg-10 d-flex justify-content-end mb-2">
        <div class="d-flex mr-1">
            <button (click)="importExcel.click()"
                [disabled]="!language"
                type="button"
                class="px-2 mb-0 py-2 btn btn-primary text-white d-flex align-items-center justify-content-between text-white cursor-pointer">
                <mat-icon>download</mat-icon>
                <span>Import</span>
            </button>
            <input class="custom-file-input d-none" #importExcel id="file" type="file" accept=".csv"
                (change)="handleFileSelect($event)">
        </div>
        <button (click)="export()"
            [disabled]="!language"
            class="px-2 mb-0 py-2 mr-2 btn btn-primary text-white d-flex align-items-center justify-content-between text-white cursor-pointer">
            <mat-icon>upload</mat-icon>
            <span>Export</span>
        </button>
    </div>
</div>
<div class="row">
    <div class="col-lg-2 ml-auto">
        <ngx-select-dropdown [multiple]="false" [(ngModel)]="language" [config]="config" (change)="onChange()"
            [options]="languages">
        </ngx-select-dropdown>
        <div *ngIf="!language" class="invalid-feedback">
            Select a language
        </div>
    </div>
    <div class="col-lg-5">
        <div class="row">
            <div class="col-lg-8 d-flex">
                <input class="form-control py-2" type="text" name="searchField" id="searchField"
                    placeholder="Search by Name or translated Content" style="margin-left:25px;" [(ngModel)]="searchField"
                    (input)="search()">
            </div>
            <div class="col-lg-4">
                <button placement="bottom" ngbTooltip="Add Kvalue"
                        [disabled]="!language"
                        class="btn btn-primary text-white form-control add-button mx-2 d-flex align-items-center justify-content-center" 
                        (click)="topicDialogOpen(true)"
                        type="button">
                    <i class="fas fa-plus d-inline fa-sm mr-1"></i>
                    <span class="custom-add d-inline">Add Kvalue</span> <!-- No hiding on smaller screens -->
                </button>
            </div>
        </div>
    </div>
    
    
</div>
<div class="row">
    <div class="col-md-12">
        <ngx-datatable class="bootstrap cursor-pointer"
            [rows]="dataSource | paginate: {itemsPerPage: 10,  currentPage: pageNumber, total: totalCount}"
            [columnMode]="ColumnMode.force" [headerHeight]="headerHeight" [footerHeight]="footerHeight"
            [rowHeight]="rowHeight" [limit]="limit" (page)="paginate($event)" [sorts]="[{prop: 'Kvalue', dir: 'asc'}]"
            [offset]="offsetPaginate" [count]="totalCount" [externalPaging]="true">
            <ngx-datatable-column name="Name" prop="Kvalue" [maxWidth]="350" [minWidth]="250">
                <ng-template let-value="value" ngx-datatable-cell-template> 
                    <span class="three-dot " matTooltip="click to copy" (click)="clickToCopy(value)">{{value || '-'}}</span> 
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="language" prop="languageId" [maxWidth]="200" [minWidth]="100">
                <ng-template let-value="value" ngx-datatable-cell-template>
                    <p style="word-break: break-all;white-space: normal;" class="mb-0">{{value.name || '-' }}</p>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Translated Content" prop="translatedContent" [maxWidth]="500" [minWidth]="300">
                <ng-template let-value="value" ngx-datatable-cell-template>
                    <p style="word-break: break-all;white-space: normal;" class="mb-0">{{value || '-' }}</p>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Action" prop="Action" [maxWidth]="250" [minWidth]="150">
                <ng-template let-row="row" let-index="index" ngx-datatable-cell-template>
                    <mat-icon color="primary" (click)="action(row, true)">edit</mat-icon>
                    <mat-icon color="primary" (click)="action(row, false)">delete</mat-icon>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>
    </div>
</div>

<ng-template #kvalue>
    <form [formGroup]="languageKForm" class="p-3">
        <div class="row" style="width: 100%;">
            <div class="col-12 mb-3">
                <label for="Kvalue" class="form-label">K value</label>
                <!--  (change)="checkAlreadyPresent()" -->
                <input class="form-control py-2" formControlName="Kvalue" type="text" name="Kvalue" id="Kvalue"
                    placeholder="Enter a Kvalue here">
                <div *ngIf="submitted && f.Kvalue?.errors?.required" class="invalid-feedback">
                    Kvalue is required
                </div>
                <div *ngIf="submitted && f.Kvalue?.errors?.notEqualActive" class="invalid-feedback">
                    Kvalue is Already Present
                </div>
            </div>
            <div class="col-12 mb-3">
                <label for="translatedContent" class="form-label">Translated Content</label>
                <input class="form-control py-2" formControlName="translatedContent" type="text"
                    name="translatedContent" id="translatedContent" placeholder="Enter here you translatedContent">
                <div *ngIf="submitted && f.translatedContent?.errors?.required" class="invalid-feedback">
                    translatedContent is required
                </div>
            </div>
            <div class="col-12 d-flex justify-content-end">
                <div>
                    <button type="button" (click)="close()" class="ml-auto btn btn-info text-white form-control">
                        close
                    </button>
                </div>
                <div class="ml-2">
                    <button type="button" placement="top" ngbTooltip="save" *ngIf="!addOrUpdate"
                        class="btn btn-primary text-white form-control" (click)="createOrUpdate()">
                        update
                    </button>
                    <button type="button" placement="top" ngbTooltip="save" *ngIf="addOrUpdate"
                        class="btn btn-primary text-white form-control" (click)="createOrUpdate()">
                        save
                    </button>
                </div>
            </div>
        </div>
    </form>
</ng-template>